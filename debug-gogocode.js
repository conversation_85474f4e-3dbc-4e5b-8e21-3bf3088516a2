const gogocode = require('gogocode');
const { transform: vueTransform } = require('gogocode-plugin-vue');
const fs = require('fs');
const path = require('path');

// 测试文件内容
const testMainJs = `
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
`;

const testVueFile = `
<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>
`;

console.log('🔍 测试 Gogocode 转换问题...\n');

// 测试 1: 直接使用 gogocode 解析
console.log('1. 测试 gogocode 基本解析:');
try {
  const ast = gogocode(testMainJs);
  console.log('✅ gogocode 基本解析成功');
  console.log('AST 类型:', typeof ast);
  console.log('AST 有 replace 方法:', typeof ast.replace === 'function');
} catch (error) {
  console.log('❌ gogocode 基本解析失败:', error.message);
}

// 测试 2: 测试 vueTransform
console.log('\n2. 测试 vueTransform:');
try {
  const result = vueTransform(
    {
      path: 'test-main.js',
      source: testMainJs,
    },
    {
      gogocode: gogocode,
    },
    {
      rootPath: process.cwd(),
      outFilePath: 'test-main.js',
      outRootPath: process.cwd(),
    }
  );
  
  console.log('✅ vueTransform 成功');
  console.log('结果类型:', typeof result);
  console.log('结果长度:', result ? result.length : 'null');
} catch (error) {
  console.log('❌ vueTransform 失败:', error.message);
  console.log('错误堆栈:', error.stack);
}

// 测试 3: 测试 Vue 文件转换
console.log('\n3. 测试 Vue 文件转换:');
try {
  const result = vueTransform(
    {
      path: 'test.vue',
      source: testVueFile,
    },
    {
      gogocode: gogocode,
    },
    {
      rootPath: process.cwd(),
      outFilePath: 'test.vue',
      outRootPath: process.cwd(),
    }
  );
  
  console.log('✅ Vue 文件转换成功');
  console.log('结果类型:', typeof result);
} catch (error) {
  console.log('❌ Vue 文件转换失败:', error.message);
  console.log('错误堆栈:', error.stack);
}

// 测试 4: 手动测试规则
console.log('\n4. 测试单个规则:');
try {
  const apiRule = require('./node_modules/.pnpm/gogocode-plugin-vue@1.0.29/node_modules/gogocode-plugin-vue/src/api.js');
  const ast = gogocode(testMainJs);

  console.log('AST 对象:', {
    type: typeof ast,
    hasReplace: typeof ast.replace === 'function',
    parseOptions: ast.parseOptions
  });

  const result = apiRule(ast);
  console.log('✅ API 规则测试成功');
  console.log('结果类型:', typeof result);
} catch (error) {
  console.log('❌ API 规则测试失败:', error.message);
  console.log('错误堆栈:', error.stack);
}

// 测试 5: 测试 Vue 文件的 script 部分
console.log('\n5. 测试 Vue 文件的 script 部分:');
try {
  const vueFileContent = `
<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
import Vue from 'vue'
import App from './App.vue'

Vue.config.productionTip = false

new Vue({
  render: h => h(App)
}).$mount('#app')
</script>
`;

  const vueAst = gogocode(vueFileContent, { parseOptions: { language: 'vue' } });
  console.log('Vue AST 创建成功');

  const scriptAST = vueAst.find('<script></script>');
  console.log('Script AST 对象:', {
    type: typeof scriptAST,
    hasReplace: typeof scriptAST.replace === 'function',
    length: scriptAST.length,
    parseOptions: scriptAST.parseOptions
  });

  // 测试 API 规则
  const apiRule = require('./node_modules/.pnpm/gogocode-plugin-vue@1.0.29/node_modules/gogocode-plugin-vue/src/api.js');
  const result = apiRule(vueAst);
  console.log('✅ Vue 文件 API 规则测试成功');

} catch (error) {
  console.log('❌ Vue 文件 API 规则测试失败:', error.message);
  console.log('错误堆栈:', error.stack);
}

// 测试 6: 测试 router 规则
console.log('\n6. 测试 router 规则:');
try {
  const routerRule = require('./node_modules/.pnpm/gogocode-plugin-vue@1.0.29/node_modules/gogocode-plugin-vue/src/vue-router.js');
  const routerCode = `
import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = []

const router = new VueRouter({
  mode: 'history',
  routes
})

export default router
`;

  const ast = gogocode(routerCode);
  console.log('Router AST 创建成功');

  const result = routerRule(ast, { gogocode });
  console.log('✅ Router 规则测试成功');
  console.log('结果类型:', typeof result);

} catch (error) {
  console.log('❌ Router 规则测试失败:', error.message);
  console.log('错误堆栈:', error.stack);
}

// 测试 7: 测试实际的问题文件
console.log('\n7. 测试实际的问题文件:');
try {
  const problemCode = `
import { createApp } from 'vue'
import VueRouter from 'vue-router'

const app = createApp({})
app.use(VueRouter)

const routes = []

const router = VueRouter.createRouter({
  history: VueRouter.createWebHistory(process.env.BASE_URL),
  routes
})

export default app
`;

  console.log('测试问题代码...');
  const ast = gogocode(problemCode);
  console.log('AST 创建成功');

  // 测试 API 规则
  const apiRule = require('./node_modules/.pnpm/gogocode-plugin-vue@1.0.29/node_modules/gogocode-plugin-vue/src/api.js');
  console.log('开始测试 API 规则...');

  // 检查 scriptAST 的类型
  const scriptAST = ast.parseOptions && ast.parseOptions.language === 'vue'
    ? ast.find('<script></script>')
    : ast;

  console.log('scriptAST 对象:', {
    type: typeof scriptAST,
    hasReplace: typeof scriptAST.replace === 'function',
    length: scriptAST.length,
    parseOptions: scriptAST.parseOptions,
    isArray: Array.isArray(scriptAST)
  });

  const result = apiRule(ast);
  console.log('✅ 问题代码 API 规则测试成功');

} catch (error) {
  console.log('❌ 问题代码测试失败:', error.message);
  console.log('错误堆栈:', error.stack);
}

// 测试 8: 测试空的 find 结果
console.log('\n8. 测试空的 find 结果:');
try {
  const ast = gogocode('console.log("hello")');
  const scriptAST = ast.find('<script></script>');

  console.log('空 find 结果:', {
    type: typeof scriptAST,
    hasReplace: typeof scriptAST.replace === 'function',
    length: scriptAST.length,
    parseOptions: scriptAST.parseOptions
  });

  // 尝试调用 replace
  if (typeof scriptAST.replace === 'function') {
    scriptAST.replace('Vue.set($_$1,$_$2,$_$3)', '$_$1[$_$2] = $_$3');
    console.log('✅ 空结果 replace 调用成功');
  } else {
    console.log('❌ 空结果没有 replace 方法');
  }

} catch (error) {
  console.log('❌ 空结果测试失败:', error.message);
  console.log('错误堆栈:', error.stack);
}
