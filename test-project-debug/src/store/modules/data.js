const state = {
  items: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
  },
  sortBy: 'id',
  sortOrder: 'asc',
}

const mutations = {
  SET_ITEMS(state, items) {
    state.items = items
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  SET_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination }
  },
  SET_FILTERS(state, filters) {
    state.filters = filters
  },
  SET_SORT(state, { sortBy, sortOrder }) {
    state.sortBy = sortBy
    state.sortOrder = sortOrder
  },
  ADD_ITEM(state, item) {
    state.items.push(item)
  },
  UPDATE_ITEM(state, { id, data }) {
    const index = state.items.findIndex((item) => item.id === id)
    if (index !== -1) {
      state.items[index] = { ...state.items[index], ...data }
    }
  },
  DELETE_ITEM(state, id) {
    const index = state.items.findIndex((item) => item.id === id)
    if (index !== -1) {
      state.items.splice(index, 1)
    }
  },
}

const actions = {
  async fetchItems({ commit }, params = {}) {
    commit('SET_LOADING', true)
    try {
      // 模拟 API 调用
      await new Promise((resolve) => setTimeout(resolve, 1000))
      const items = Array.from({ length: 20 }, (_, i) => ({
        id: i + 1,
        name: `Item ${i + 1}`,
        description: `Description for item ${i + 1}`,
        status: i % 2 === 0 ? 'active' : 'inactive',
        createdAt: new Date(Date.now() - Math.random() * 10000000000),
      }))
      commit('SET_ITEMS', items)
      commit('SET_PAGINATION', { total: items.length })
    } catch (error) {
      console.error('Failed to fetch items:', error)
    } finally {
      commit('SET_LOADING', false)
    }
  },
  async createItem({ commit }, itemData) {
    commit('SET_LOADING', true)
    try {
      // 模拟 API 调用
      await new Promise((resolve) => setTimeout(resolve, 500))
      const newItem = {
        id: Date.now(),
        ...itemData,
        createdAt: new Date(),
      }
      commit('ADD_ITEM', newItem)
      return newItem
    } catch (error) {
      console.error('Failed to create item:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  async updateItem({ commit }, { id, data }) {
    commit('SET_LOADING', true)
    try {
      // 模拟 API 调用
      await new Promise((resolve) => setTimeout(resolve, 500))
      commit('UPDATE_ITEM', { id, data })
    } catch (error) {
      console.error('Failed to update item:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  async deleteItem({ commit }, id) {
    commit('SET_LOADING', true)
    try {
      // 模拟 API 调用
      await new Promise((resolve) => setTimeout(resolve, 500))
      commit('DELETE_ITEM', id)
    } catch (error) {
      console.error('Failed to delete item:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  setFilters({ commit }, filters) {
    commit('SET_FILTERS', filters)
  },
  setSort({ commit }, sortConfig) {
    commit('SET_SORT', sortConfig)
  },
}

const getters = {
  items: (state) => state.items,
  loading: (state) => state.loading,
  pagination: (state) => state.pagination,
  filters: (state) => state.filters,
  sortBy: (state) => state.sortBy,
  sortOrder: (state) => state.sortOrder,
  filteredItems: (state) => {
    let items = [...state.items]

    // 应用过滤器
    if (state.filters.status) {
      items = items.filter((item) => item.status === state.filters.status)
    }
    if (state.filters.search) {
      items = items.filter(
        (item) =>
          item.name
            .toLowerCase()
            .includes(state.filters.search.toLowerCase()) ||
          item.description
            .toLowerCase()
            .includes(state.filters.search.toLowerCase())
      )
    }

    // 应用排序
    items.sort((a, b) => {
      const aVal = a[state.sortBy]
      const bVal = b[state.sortBy]
      if (state.sortOrder === 'asc') {
        return aVal > bVal ? 1 : -1
      } else {
        return aVal < bVal ? 1 : -1
      }
    })

    return items
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
}
