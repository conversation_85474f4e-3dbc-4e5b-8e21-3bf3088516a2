<template>
  <div class="home">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template v-slot:header>
            <div>
              <span>Welcome to Vue 2 Test Project</span>
            </div>
          </template>
          <div class="welcome-content">
            <h2>Vue 2 to Vue 3 Migration Test Project</h2>
            <p>
              This project contains various Vue 2 components and patterns that
              need to be migrated to Vue 3.
            </p>

            <el-row :gutter="20" class="feature-grid">
              <el-col :span="8">
                <el-card class="feature-card">
                  <template v-slot:header>
                    <div>
                      <el-icon><el-icon-s-grid /></el-icon>
                      Components
                    </div>
                  </template>
                  <p>
                    Test various Vue 2 components including third-party
                    libraries
                  </p>
                  <el-button
                    type="primary"
                    @click="$router.push('/components')"
                  >
                    View Components
                  </el-button>
                </el-card>
              </el-col>

              <el-col :span="8">
                <el-card class="feature-card">
                  <template v-slot:header>
                    <div>
                      <el-icon><el-icon-s-marketing /></el-icon>
                      Charts
                    </div>
                  </template>
                  <p>Test chart libraries and data visualization components</p>
                  <el-button type="primary" @click="$router.push('/charts')">
                    View Charts
                  </el-button>
                </el-card>
              </el-col>

              <el-col :span="8">
                <el-card class="feature-card">
                  <template v-slot:header>
                    <div>
                      <el-icon><el-icon-edit /></el-icon>
                      Forms
                    </div>
                  </template>
                  <p>Test form components and validation patterns</p>
                  <el-button type="primary" @click="$router.push('/forms')">
                    View Forms
                  </el-button>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card>
          <template v-slot:header>
            <div>
              <span>Quick Stats</span>
            </div>
          </template>
          <div class="stats">
            <div class="stat-item">
              <count-to :start-val="0" :end-val="42" :duration="2000" />
              <span>Components</span>
            </div>
            <div class="stat-item">
              <count-to :start-val="0" :end-val="15" :duration="2000" />
              <span>Pages</span>
            </div>
            <div class="stat-item">
              <count-to :start-val="0" :end-val="8" :duration="2000" />
              <span>Libraries</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template v-slot:header>
            <div>
              <span>Recent Activity</span>
            </div>
          </template>
          <div class="activity-list">
            <div
              class="activity-item"
              v-for="activity in recentActivities"
              :key="activity.id"
            >
              <i :class="activity.icon"></i>
              <span>{{ activity.text }}</span>
              <small>{{ activity.time }}</small>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  SGrid as ElIconSGrid,
  SMarketing as ElIconSMarketing,
  Edit as ElIconEdit,
} from '@element-plus/icons'
import CountTo from 'vue-count-to'

export default {
  components: {
    CountTo,
    ElIconSGrid,
    ElIconSMarketing,
    ElIconEdit,
  },
  name: 'Home',
  data() {
    return {
      recentActivities: [
        {
          id: 1,
          text: 'Component library updated',
          icon: 'el-icon-s-grid',
          time: '2 hours ago',
        },
        {
          id: 2,
          text: 'New chart component added',
          icon: 'el-icon-s-marketing',
          time: '4 hours ago',
        },
        {
          id: 3,
          text: 'Form validation improved',
          icon: 'el-icon-edit',
          time: '6 hours ago',
        },
        {
          id: 4,
          text: 'Table component migrated',
          icon: 'el-icon-s-order',
          time: '1 day ago',
        },
      ],
    }
  },
  mounted() {
    this.$message.success('Welcome to Vue 2 Test Project!')
  },
}
</script>

<style scoped>
.home {
  padding: 20px;
}
.welcome-content {
  text-align: center;
}
.welcome-content h2 {
  color: #409eff;
  margin-bottom: 20px;
}
.feature-grid {
  margin-top: 30px;
}
.feature-card {
  text-align: center;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.feature-card .el-card__header {
  background-color: #f5f7fa;
}
.stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-item span {
  margin-top: 10px;
  color: #666;
}
.activity-list {
  max-height: 200px;
  overflow-y: auto;
}
.activity-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}
.activity-item:last-child {
  border-bottom: none;
}
.activity-item i {
  margin-right: 10px;
  color: #409eff;
}
.activity-item span {
  flex: 1;
}
.activity-item small {
  color: #999;
}
</style>
